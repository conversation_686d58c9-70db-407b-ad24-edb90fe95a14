output "api_acm_arn" {
  description = "API Certificate ARN"
  value       = aws_acm_certificate.comtech_smartanalytics_certificate.arn
}

output "api_subdomain_name" {
  description = "Subdomain name for the api"
  value       = local.api_subdomain_name
}

output "domain_hosted_zone_id" {
  description = "Domain hosted zone id"
  value       = data.aws_route53_zone.domain_hosted_zone.zone_id
}

output "redshift_acm_arn" {
  description = "Redshift Certificate ARN"
  value       = aws_acm_certificate_validation.redshift_certificate_validation.certificate_arn
}

output "redshift_subdomain_name" {
  description = "Subdomain name for Redshift"
  value       = local.redshift_subdomain_name
}