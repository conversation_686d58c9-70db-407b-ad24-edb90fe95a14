resource "aws_ssm_parameter" "redshift_credentials" {
  depends_on = [
    random_password.redshift_master_password
  ]

  name        = "/${var.environment}/${var.country}/smartanalytics/${var.customer}_redshift_credentials"
  description = "${var.customer} redshift credentials"
  type        = "SecureString"
  value = jsonencode({
    "master_username" : var.redshift_master_username,
    "master_password" : random_password.redshift_master_password.result,
    "endpoint" : "${var.redshift_subdomain_name}:${var.redshift_port}",
    "fqdn" : aws_route53_record.redshift_cname_record.fqdn,
    "aws_endpoint" : aws_redshift_cluster.redshift_cluster.endpoint
  })

  tags = {
    Name = "${local.prefix}-redshift-credentials"
  }
}
