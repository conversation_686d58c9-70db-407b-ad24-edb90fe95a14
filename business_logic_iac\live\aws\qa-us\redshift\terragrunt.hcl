dependency "network" {
  config_path = "../network"
}

dependency "dns" {
  config_path = "../dns"
}

inputs = {
  aws_region               = "us-east-1"
  aws_vpc_id               = dependency.network.outputs.vpc_id
  customer                 = "common"
  country                  = "us"
  domain_hosted_zone_id    = dependency.dns.outputs.domain_hosted_zone_id
  environment              = "qa"
  private_subnets_ids      = dependency.network.outputs.private_subnets_ids
  redshift_cluster_type    = "single-node"
  redshift_port            = 5439
  redshift_node_type       = "ra3.large"
  redshift_number_of_nodes = 1
  redshift_acm_arn         = dependency.dns.outputs.redshift_acm_arn
  redshift_subdomain_name  = dependency.dns.outputs.redshift_subdomain_name
  tags                     = merge(
    include.root.locals.tags,
    {
      ChargeCode  = "03SMAL.GENN.0000.WA9.DEV"
      country     = "us"
      environment = "qa"
    }
  )
  vpc_cidr_block           = dependency.network.outputs.vpc_cidr_block
}

terraform {
  source = "../../../../modules/redshift/"
  #source = "git::ssh://****************************/cloud-infrastructure/comtechanalytics-infrastructure.git//business_logic_iac/modules/redshift?ref=main"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}
