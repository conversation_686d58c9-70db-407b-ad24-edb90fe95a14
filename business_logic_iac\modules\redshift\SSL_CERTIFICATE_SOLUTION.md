# Redshift SSL Certificate Solution

## Problem
Power BI's Redshift ODBC driver was failing with SSL hostname mismatch errors when connecting to Reds<PERSON>ft through a CNAME record. The error occurred because:

- CNAME: `dev-us-smartanalytics-common-redshift.dev-nsoc.state911.net`
- AWS Certificate: `*.csj8hprvfldf.us-east-1.redshift.amazonaws.com`
- Power BI's `verify-full` SSL mode requires exact hostname match

## Solution Overview
Implemented a professional SSL certificate solution using AWS Certificate Manager (ACM) that provides:

1. **Custom SSL Certificate**: ACM certificate for the custom domain
2. **DNS Validation**: Automatic certificate validation via Route53
3. **Professional Endpoint**: Stable, custom domain that won't change on deployments
4. **SSL Parameter Group**: Redshift parameter group enforcing SSL connections

## Implementation Details

### 1. DNS Module Updates
- **Added**: `redshift_subdomain_name` local variable
- **Added**: `aws_acm_certificate.redshift_certificate` resource
- **Added**: DNS validation records for certificate
- **Added**: Outputs for certificate ARN and subdomain name

### 2. Redshift Module Updates
- **Added**: Redshift parameter group with `require_ssl = true`
- **Updated**: Cluster configuration to use parameter group
- **Added**: Variables for certificate ARN and subdomain name
- **Updated**: Route53 record to use custom subdomain
- **Updated**: Outputs to provide SSL-ready endpoint
- **Updated**: SSM parameter with multiple endpoint options

### 3. Terragrunt Configuration Updates
- **Added**: Certificate ARN dependency from DNS module
- **Added**: Subdomain name dependency from DNS module
- **Updated**: Both dev-us and qa-us environments

## Usage

### For Power BI Desktop
Use the custom domain endpoint from the Redshift module output:
```
Endpoint: dev-us-smartanalytics-common-redshift.dev-nsoc.state911.net:5439
SSL Mode: require (or verify-full)
```

### For Applications
The SSM parameter now contains multiple endpoint options:
```json
{
  "master_username": "solacom",
  "master_password": "...",
  "endpoint": "dev-us-smartanalytics-common-redshift.dev-nsoc.state911.net:5439",
  "fqdn": "dev-us-smartanalytics-common-redshift.dev-nsoc.state911.net",
  "aws_endpoint": "dev-us-smartanalytics-common-redshift.csj8hprvfldf.us-east-1.redshift.amazonaws.com:5439"
}
```

## Deployment Steps

1. **Deploy DNS Module First**:
   ```bash
   cd business_logic_iac/live/aws/dev-us/dns
   terragrunt apply
   ```

2. **Deploy Redshift Module**:
   ```bash
   cd business_logic_iac/live/aws/dev-us/redshift
   terragrunt apply
   ```

3. **Verify Certificate**:
   - Check ACM console for certificate validation
   - Verify Route53 records are created
   - Test SSL connection

## Benefits

1. **Professional**: Custom domain that doesn't change with deployments
2. **Secure**: Enforced SSL connections with proper certificate validation
3. **Stable**: No more hostname mismatch errors
4. **Scalable**: Same pattern can be applied to other environments
5. **Maintainable**: Integrated with existing DNS and certificate infrastructure

## Troubleshooting

### Certificate Validation Issues
- Ensure Route53 hosted zone has proper permissions
- Check DNS propagation for validation records
- Verify certificate status in ACM console

### Connection Issues
- Verify security group allows port 5439 from your IP/VPC
- Check Redshift cluster is in running state
- Confirm parameter group is attached to cluster

### Power BI Connection
- Use the exact subdomain name from outputs
- Set SSL mode to `require` or `verify-full`
- Ensure you're connecting from within VPC or through VPN

## Security Considerations

- SSL is enforced at the Redshift level via parameter group
- Certificate is managed by AWS Certificate Manager
- Private subnets ensure Redshift is not publicly accessible
- Security groups restrict access to authorized sources only
