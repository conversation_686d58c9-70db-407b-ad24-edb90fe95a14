resource "aws_route53_record" "comtech_smartanalytics_public_record" {
  for_each = {
    for dvo in aws_acm_certificate.comtech_smartanalytics_certificate.domain_validation_options : dvo.domain_name => {
      name   = dvo.resource_record_name
      record = dvo.resource_record_value
      type   = dvo.resource_record_type
    }
  }
  allow_overwrite = true
  name            = each.value.name
  records         = [each.value.record]
  ttl             = 300
  type            = each.value.type
  zone_id         = data.aws_route53_zone.domain_hosted_zone.zone_id
}


# DNS validation records for Redshift certificate
resource "aws_route53_record" "redshift_validation_record" {
  for_each = {
    for dvo in aws_acm_certificate.redshift_certificate.domain_validation_options : dvo.domain_name => {
      name   = dvo.resource_record_name
      record = dvo.resource_record_value
      type   = dvo.resource_record_type
    }
  }
  allow_overwrite = true
  name            = each.value.name
  records         = [each.value.record]
  ttl             = 300
  type            = each.value.type
  zone_id         = data.aws_route53_zone.domain_hosted_zone.zone_id
}