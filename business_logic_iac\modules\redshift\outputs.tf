output "redshift_cluster_identifier" {
  description = "Customer Redshift cluster identifier"
  value       = aws_redshift_cluster.redshift_cluster.cluster_identifier
}


output "redshift_endpoint" {
  description = "Customer Redshift endpoint"
  value       = "${var.redshift_subdomain_name}:${var.redshift_port}"
}

output "redshift_endpoint_aws" {
  description = "Customer Redshift AWS endpoint"
  value       = aws_redshift_cluster.redshift_cluster.endpoint
}

output "redshift_fqdn" {
  description = "Customer Redshift FQDN"
  value       = aws_route53_record.redshift_cname_record.fqdn
}


output "redshift_subnet_ids" {
  description = "Customer Redshift subnet ids"
  value       = aws_redshift_subnet_group.redshift_subnet_group.subnet_ids
}


output "redshift_ssm_parameter_name" {
  description = "Customer Redshift SSM parameter name"
  value       = aws_ssm_parameter.redshift_credentials.name
}


output "redshift_vpc_security_group_ids" {
  description = "Customer Redshift VPC security group ids"
  value       = aws_redshift_cluster.redshift_cluster.vpc_security_group_ids
}
